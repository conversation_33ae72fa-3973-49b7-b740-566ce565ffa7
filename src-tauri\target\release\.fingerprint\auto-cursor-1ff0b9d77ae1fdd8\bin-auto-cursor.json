{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 8188219801901903984, "profile": 2040997289075261528, "path": 4942398508502643691, "deps": [[503635761244294217, "regex", false, 9132613482486827673], [530211389790465181, "hex", false, 14557464141462546839], [1996688857878793156, "<PERSON><PERSON><PERSON><PERSON>", false, 6011534536139471130], [2995469292676432503, "uuid", false, 3169118297239625609], [3239934230994155792, "tauri", false, 6641134217383665854], [4352886507220678900, "serde_json", false, 16593166059665494449], [7244058819997729774, "reqwest", false, 5095384295636573572], [8008191657135824715, "thiserror", false, 517237222325720486], [8256202458064874477, "dirs", false, 5361534918694968737], [8324132117207348776, "rusqlite", false, 1106904137063231543], [8405603588346937335, "winreg", false, 9630239009349378660], [9689903380558560274, "serde", false, 5296979348095575602], [9857275760291862238, "sha2", false, 10801582607383767803], [9897246384292347999, "chrono", false, 12445001927370330021], [11207653606310558077, "anyhow", false, 2516105185771174108], [12602218467368646123, "auto_cursor_lib", false, 1049586953999974284], [12602218467368646123, "build_script_build", false, 15350162637408572927], [13208667028893622512, "rand", false, 7896118256818811400], [14285978758320820277, "tauri_plugin_fs", false, 16032084631923553934], [16429266147849286097, "tauri_plugin_opener", false, 5211335565707674482], [17531218394775549125, "tokio", false, 3546806687187485960], [18066890886671768183, "base64", false, 7371817318444798226]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\auto-cursor-1ff0b9d77ae1fdd8\\dep-bin-auto-cursor", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}